# PHP Email Client

A simple, responsive PHP email client that connects to cPanel email accounts via IMAP. This webmail interface allows you to read, compose, and manage emails directly from your web browser.

## ✨ Features

- 📥 **Inbox Management** - View and organize your emails
- 📧 **Read Emails** - View full email content including HTML and plain text
- ✏️ **Compose & Send** - Write and send new emails
- ↩️ **Reply to Emails** - Quick reply functionality
- 📎 **Attachment Support** - Download email attachments
- 🗑️ **Delete Emails** - Remove unwanted emails
- 📱 **Responsive Design** - Works on desktop and mobile devices
- 🔒 **Secure Connection** - Uses IMAP over SSL/TLS

## 🛠️ Requirements

- PHP 7.4 or higher
- PHP IMAP extension enabled
- Web server (Apache, Nginx, etc.)
- cPanel email account or IMAP-compatible email server

## 📦 Installation

1. **Download/Clone** this repository to your web server
2. **Enable IMAP Extension** - Make sure `php_imap` extension is enabled in your PHP configuration
3. **Configure Email Settings** - Edit `config.php` with your email credentials
4. **Set Permissions** - Ensure web server can read/write to the directory
5. **Access via Browser** - Navigate to the installation directory

## ⚙️ Configuration

Edit the `config.php` file with your email server details:

```php
// Email Server Configuration
define('MAIL_SERVER', 'mail.yourdomain.com'); // Your mail server
define('MAIL_PORT', '993'); // IMAP SSL port
define('MAIL_FLAGS', '/imap/ssl'); // IMAP flags

// Email Credentials
define('EMAIL_ADDRESS', '<EMAIL>');
define('EMAIL_PASSWORD', 'yourEmailPassword');

// SMTP Configuration for sending emails
define('SMTP_SERVER', 'mail.yourdomain.com');
define('SMTP_PORT', '587'); // or 465 for SSL
```

### Common cPanel Email Settings:

| Setting | Value |
|---------|-------|
| **IMAP Server** | mail.yourdomain.com |
| **IMAP Port** | 993 (SSL) or 143 (non-SSL) |
| **SMTP Server** | mail.yourdomain.com |
| **SMTP Port** | 587 (TLS) or 465 (SSL) |

## 📁 File Structure

```
email_client/
├── index.php              # Main inbox view
├── view_email.php          # Individual email viewer
├── compose.php             # Compose new emails
├── settings.php            # Configuration & testing
├── download_attachment.php # Attachment downloader
├── config.php              # Email configuration
├── email_functions.php     # Core IMAP functions
├── assets/
│   └── style.css          # Stylesheet
├── includes/
│   ├── header.php         # Common header
│   └── footer.php         # Common footer
└── README.md              # This file
```

## 🚀 Usage

1. **Access the Application** - Open your browser and navigate to the installation directory
2. **Check Settings** - Visit the Settings page to test your email connection
3. **View Inbox** - Browse your emails on the main page
4. **Read Emails** - Click on any email to view its full content
5. **Compose Emails** - Use the Compose button to send new emails
6. **Reply to Emails** - Use the Reply button when viewing an email

## 🔧 Troubleshooting

### IMAP Extension Not Found
```bash
# On Ubuntu/Debian
sudo apt-get install php-imap

# On CentOS/RHEL
sudo yum install php-imap

# Don't forget to restart your web server
sudo service apache2 restart
```

### SSL Connection Issues
If you're having SSL connection problems, try:
- Using port 143 without SSL: `define('MAIL_FLAGS', '/imap/notls');`
- Disabling SSL verification: `define('MAIL_FLAGS', '/imap/ssl/novalidate-cert');`

### Authentication Failures
- Double-check your email address and password
- Some servers require app-specific passwords
- Ensure IMAP is enabled in your email account settings

### Can't Send Emails
- Verify SMTP settings in `config.php`
- Check if PHP `mail()` function is working
- Some servers require authentication for SMTP

## 🔒 Security Considerations

- **Never commit credentials** to version control
- **Use HTTPS** in production environments
- **Sanitize all output** to prevent XSS attacks
- **Validate all input** to prevent injection attacks
- **Consider using environment variables** for sensitive configuration

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues:

1. Check the Settings page for connection testing
2. Review the Troubleshooting section above
3. Ensure all PHP extensions are properly installed
4. Contact your hosting provider for server-specific issues

## 🎯 Future Enhancements

- [ ] Folder management (Sent, Drafts, Trash)
- [ ] Email search functionality
- [ ] Multiple account support
- [ ] Email filters and rules
- [ ] Rich text editor for composing
- [ ] Email templates
- [ ] Contact management
- [ ] Calendar integration

---

**Made with ❤️ for simple email management**
