<?php
/**
 * Email Client - View Single Email
 */

require_once 'config.php';
require_once 'email_functions.php';

// Get email ID from URL
$emailId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$emailId) {
    $_SESSION['message'] = 'Invalid email ID';
    $_SESSION['message_type'] = 'error';
    header('Location: index.php');
    exit;
}

// Handle delete action
if (isset($_POST['delete']) && $_POST['delete'] == '1') {
    try {
        deleteEmail($emailId);
        $_SESSION['message'] = 'Email deleted successfully';
        $_SESSION['message_type'] = 'success';
        header('Location: index.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['message'] = 'Error deleting email: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
}

try {
    $email = getEmail($emailId);
    $pageTitle = 'Email: ' . $email['subject'];
} catch (Exception $e) {
    $_SESSION['message'] = 'Error loading email: ' . $e->getMessage();
    $_SESSION['message_type'] = 'error';
    header('Location: index.php');
    exit;
}

include 'includes/header.php';
?>

<div class="email-view">
    <div style="margin-bottom: 20px;">
        <a href="index.php" class="btn btn-secondary">← Back to Inbox</a>
        <a href="compose.php?reply=<?php echo $emailId; ?>" class="btn">↩️ Reply</a>
        
        <form method="post" style="display: inline-block; margin-left: 10px;">
            <input type="hidden" name="delete" value="1">
            <button type="submit" class="btn btn-danger btn-small">🗑️ Delete</button>
        </form>
    </div>
    
    <div class="email-header">
        <h1 class="email-subject-large">
            <?php echo sanitizeOutput($email['subject']); ?>
        </h1>
        
        <div class="email-meta">
            <span class="email-meta-label">From:</span>
            <span class="email-meta-value">
                <?php 
                $from = $email['from'];
                if (isset($from->personal)) {
                    echo sanitizeOutput($from->personal) . ' &lt;' . sanitizeOutput($from->mailbox . '@' . $from->host) . '&gt;';
                } else {
                    echo sanitizeOutput($from->mailbox . '@' . $from->host);
                }
                ?>
            </span>
            
            <?php if (!empty($email['to'])): ?>
                <span class="email-meta-label">To:</span>
                <span class="email-meta-value">
                    <?php
                    $toList = [];
                    foreach ($email['to'] as $to) {
                        if (isset($to->personal)) {
                            $toList[] = sanitizeOutput($to->personal) . ' &lt;' . sanitizeOutput($to->mailbox . '@' . $to->host) . '&gt;';
                        } else {
                            $toList[] = sanitizeOutput($to->mailbox . '@' . $to->host);
                        }
                    }
                    echo implode(', ', $toList);
                    ?>
                </span>
            <?php endif; ?>
            
            <?php if (!empty($email['cc'])): ?>
                <span class="email-meta-label">CC:</span>
                <span class="email-meta-value">
                    <?php
                    $ccList = [];
                    foreach ($email['cc'] as $cc) {
                        if (isset($cc->personal)) {
                            $ccList[] = sanitizeOutput($cc->personal) . ' &lt;' . sanitizeOutput($cc->mailbox . '@' . $cc->host) . '&gt;';
                        } else {
                            $ccList[] = sanitizeOutput($cc->mailbox . '@' . $cc->host);
                        }
                    }
                    echo implode(', ', $ccList);
                    ?>
                </span>
            <?php endif; ?>
            
            <span class="email-meta-label">Date:</span>
            <span class="email-meta-value">
                <?php echo date('F j, Y \a\t g:i A', strtotime($email['date'])); ?>
            </span>
            
            <span class="email-meta-label">Size:</span>
            <span class="email-meta-value">
                <?php echo formatBytes($email['size']); ?>
            </span>
        </div>
        
        <?php if (!empty($email['attachments'])): ?>
            <div style="margin-top: 20px;">
                <strong>📎 Attachments:</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <?php foreach ($email['attachments'] as $attachment): ?>
                        <li>
                            <strong><?php echo sanitizeOutput($attachment['filename']); ?></strong>
                            (<?php echo formatBytes($attachment['size']); ?>)
                            <a href="download_attachment.php?email=<?php echo $emailId; ?>&part=<?php echo $attachment['part']; ?>&filename=<?php echo urlencode($attachment['filename']); ?>" 
                               class="btn btn-small" style="margin-left: 10px;">Download</a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="email-body">
        <?php
        $body = $email['body'];
        
        // Check if it's HTML content
        if (strip_tags($body) != $body) {
            // HTML email - display as HTML but sanitize dangerous elements
            $body = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $body);
            $body = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $body);
            echo $body;
        } else {
            // Plain text email - preserve formatting
            echo '<pre>' . sanitizeOutput($body) . '</pre>';
        }
        ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
