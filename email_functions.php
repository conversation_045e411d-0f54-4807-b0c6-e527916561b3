<?php
/**
 * Email Functions Library
 * 
 * Core IMAP functions for the email client
 */

require_once 'config.php';

/**
 * Connect to IMAP server
 */
function connectToImap() {
    $hostname = getImapConnection();
    $inbox = imap_open($hostname, EMAIL_ADDRESS, EMAIL_PASSWORD);
    
    if (!$inbox) {
        throw new Exception('Cannot connect to email server: ' . imap_last_error());
    }
    
    return $inbox;
}

/**
 * Get list of emails
 */
function getEmails($page = 1, $perPage = EMAILS_PER_PAGE) {
    $inbox = connectToImap();
    
    // Get all emails
    $emails = imap_search($inbox, 'ALL');
    
    if (!$emails) {
        imap_close($inbox);
        return ['emails' => [], 'total' => 0, 'pages' => 0];
    }
    
    // Sort newest first
    rsort($emails);
    
    $total = count($emails);
    $totalPages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    
    // Get emails for current page
    $pageEmails = array_slice($emails, $offset, $perPage);
    $emailList = [];
    
    foreach ($pageEmails as $emailNumber) {
        $overview = imap_fetch_overview($inbox, $emailNumber, 0)[0];
        $header = imap_headerinfo($inbox, $emailNumber);
        
        $emailList[] = [
            'number' => $emailNumber,
            'subject' => isset($overview->subject) ? $overview->subject : '(No Subject)',
            'from' => $overview->from,
            'date' => $overview->date,
            'size' => $overview->size,
            'seen' => $overview->seen,
            'flagged' => $overview->flagged,
            'answered' => $overview->answered,
            'uid' => $overview->uid
        ];
    }
    
    imap_close($inbox);
    
    return [
        'emails' => $emailList,
        'total' => $total,
        'pages' => $totalPages,
        'current_page' => $page
    ];
}

/**
 * Get single email details
 */
function getEmail($emailNumber) {
    $inbox = connectToImap();
    
    // Get email overview
    $overview = imap_fetch_overview($inbox, $emailNumber, 0)[0];
    $header = imap_headerinfo($inbox, $emailNumber);
    
    // Get email structure
    $structure = imap_fetchstructure($inbox, $emailNumber);
    
    // Get email body
    $body = getEmailBody($inbox, $emailNumber, $structure);
    
    // Mark as read
    imap_setflag_full($inbox, $emailNumber, "\\Seen");
    
    $email = [
        'number' => $emailNumber,
        'subject' => isset($overview->subject) ? $overview->subject : '(No Subject)',
        'from' => $header->from[0],
        'to' => isset($header->to) ? $header->to : [],
        'cc' => isset($header->cc) ? $header->cc : [],
        'date' => $header->date,
        'size' => $overview->size,
        'body' => $body,
        'attachments' => getAttachments($inbox, $emailNumber, $structure)
    ];
    
    imap_close($inbox);
    return $email;
}

/**
 * Get email body (handles multipart emails)
 */
function getEmailBody($inbox, $emailNumber, $structure) {
    $body = '';
    
    if (isset($structure->parts) && count($structure->parts)) {
        // Multipart email
        for ($i = 0; $i < count($structure->parts); $i++) {
            $part = $structure->parts[$i];
            
            if ($part->type == 0) { // Text
                $partBody = imap_fetchbody($inbox, $emailNumber, $i + 1);
                
                // Decode based on encoding
                if ($part->encoding == 3) { // Base64
                    $partBody = base64_decode($partBody);
                } elseif ($part->encoding == 4) { // Quoted-printable
                    $partBody = quoted_printable_decode($partBody);
                }
                
                $body .= $partBody;
            }
        }
    } else {
        // Simple email
        $body = imap_fetchbody($inbox, $emailNumber, 1);
        
        if ($structure->encoding == 3) { // Base64
            $body = base64_decode($body);
        } elseif ($structure->encoding == 4) { // Quoted-printable
            $body = quoted_printable_decode($body);
        }
    }
    
    return $body;
}

/**
 * Get email attachments
 */
function getAttachments($inbox, $emailNumber, $structure) {
    $attachments = [];
    
    if (isset($structure->parts) && count($structure->parts)) {
        for ($i = 0; $i < count($structure->parts); $i++) {
            $part = $structure->parts[$i];
            
            if (isset($part->disposition) && $part->disposition == 'ATTACHMENT') {
                $filename = '';
                
                if (isset($part->dparameters)) {
                    foreach ($part->dparameters as $param) {
                        if (strtolower($param->attribute) == 'filename') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }
                
                if (isset($part->parameters)) {
                    foreach ($part->parameters as $param) {
                        if (strtolower($param->attribute) == 'name') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }
                
                if ($filename) {
                    $attachments[] = [
                        'filename' => $filename,
                        'size' => $part->bytes,
                        'part' => $i + 1
                    ];
                }
            }
        }
    }
    
    return $attachments;
}

/**
 * Send email using PHP mail function
 */
function sendEmail($to, $subject, $message, $from = EMAIL_ADDRESS) {
    $headers = "From: $from\r\n";
    $headers .= "Reply-To: $from\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

/**
 * Delete email
 */
function deleteEmail($emailNumber) {
    $inbox = connectToImap();
    
    // Mark for deletion
    imap_delete($inbox, $emailNumber);
    
    // Expunge to actually delete
    imap_expunge($inbox);
    
    imap_close($inbox);
    return true;
}
?>
