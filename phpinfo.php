<?php
// PHP Info page to check loaded extensions
echo "<h1>PHP Configuration Check</h1>";

echo "<h2>PHP Version</h2>";
echo "<p>" . PHP_VERSION . "</p>";

echo "<h2>IMAP Extension Status</h2>";
if (extension_loaded('imap')) {
    echo "<p style='color: green; font-weight: bold;'>✅ IMAP Extension is LOADED</p>";
    
    // Show IMAP functions
    $imap_functions = get_extension_funcs('imap');
    echo "<h3>Available IMAP Functions (" . count($imap_functions) . "):</h3>";
    echo "<ul>";
    foreach (array_slice($imap_functions, 0, 10) as $func) {
        echo "<li>$func</li>";
    }
    if (count($imap_functions) > 10) {
        echo "<li>... and " . (count($imap_functions) - 10) . " more</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ IMAP Extension is NOT LOADED</p>";
}

echo "<h2>Other Important Extensions</h2>";
$important_extensions = ['openssl', 'curl', 'mbstring', 'json'];
foreach ($important_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $ext</p>";
}

echo "<h2>PHP Configuration File</h2>";
echo "<p><strong>Loaded php.ini:</strong> " . php_ini_loaded_file() . "</p>";

echo "<h2>All Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<p>Total: " . count($extensions) . " extensions loaded</p>";
echo "<div style='columns: 3; column-gap: 20px;'>";
foreach ($extensions as $ext) {
    echo "<p>• $ext</p>";
}
echo "</div>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Email Client</a></p>";

// Uncomment the line below to see full phpinfo()
// phpinfo();
?>
