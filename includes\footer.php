    </div> <!-- End container -->
    
    <footer style="text-align: center; padding: 20px; color: #666; margin-top: 40px;">
        <p>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?> - Simple PHP Email Client</p>
        <p style="font-size: 0.9em; margin-top: 5px;">
            Connected to: <?php echo sanitizeOutput(EMAIL_ADDRESS); ?>
        </p>
    </footer>

    <script>
        // Simple JavaScript for better UX
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
            
            // Confirm delete actions
            const deleteButtons = document.querySelectorAll('.btn-danger');
            deleteButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    if (!confirm('Are you sure you want to delete this email?')) {
                        e.preventDefault();
                    }
                });
            });
            
            // Email list click handling
            const emailItems = document.querySelectorAll('.email-item');
            emailItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    const emailNumber = this.dataset.emailNumber;
                    if (emailNumber) {
                        window.location.href = 'view_email.php?id=' + emailNumber;
                    }
                });
            });
        });
    </script>
</body>
</html>
