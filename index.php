<?php
/**
 * Email Client - Inbox View
 */

require_once 'config.php';
require_once 'email_functions.php';

$pageTitle = 'Inbox';

// Get current page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page);

try {
    // Get emails for current page
    $emailData = getEmails($page);
    $emails = $emailData['emails'];
    $totalEmails = $emailData['total'];
    $totalPages = $emailData['pages'];
    $currentPage = $emailData['current_page'];
    
} catch (Exception $e) {
    $_SESSION['message'] = 'Error connecting to email server: ' . $e->getMessage();
    $_SESSION['message_type'] = 'error';
    $emails = [];
    $totalEmails = 0;
    $totalPages = 0;
    $currentPage = 1;
}

include 'includes/header.php';
?>

<div class="email-list">
    <?php if (empty($emails)): ?>
        <div style="padding: 40px; text-align: center; color: #666;">
            <h3>📭 No emails found</h3>
            <p>Your inbox is empty or there was an error connecting to the email server.</p>
            <?php if (isset($_SESSION['message'])): ?>
                <p style="margin-top: 20px;">
                    <a href="settings.php" class="btn">⚙️ Check Settings</a>
                </p>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div style="padding: 20px; border-bottom: 2px solid #eee; background: #f8f9fa;">
            <h2>📥 Inbox (<?php echo $totalEmails; ?> emails)</h2>
            <p style="color: #666; margin-top: 5px;">
                Page <?php echo $currentPage; ?> of <?php echo $totalPages; ?>
            </p>
        </div>
        
        <?php foreach ($emails as $email): ?>
            <div class="email-item <?php echo $email['seen'] ? '' : 'unread'; ?>" 
                 data-email-number="<?php echo $email['number']; ?>">
                
                <div class="email-status">
                    <?php if (!$email['seen']): ?>
                        <span class="unread-dot" title="Unread"></span>
                    <?php endif; ?>
                    <?php if ($email['flagged']): ?>
                        <span style="color: #ffc107;" title="Flagged">⭐</span>
                    <?php endif; ?>
                    <?php if ($email['answered']): ?>
                        <span style="color: #28a745;" title="Replied">↩️</span>
                    <?php endif; ?>
                </div>
                
                <div class="email-content">
                    <div class="email-subject">
                        <?php echo sanitizeOutput($email['subject']); ?>
                    </div>
                    <div class="email-from">
                        From: <?php echo sanitizeOutput($email['from']); ?>
                    </div>
                    <div class="email-date">
                        <?php echo date('M j, Y g:i A', strtotime($email['date'])); ?>
                    </div>
                </div>
                
                <div class="email-size">
                    <?php echo formatBytes($email['size']); ?>
                </div>
            </div>
        <?php endforeach; ?>
        
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($currentPage > 1): ?>
                    <a href="?page=1">« First</a>
                    <a href="?page=<?php echo $currentPage - 1; ?>">‹ Previous</a>
                <?php endif; ?>
                
                <?php
                $startPage = max(1, $currentPage - 2);
                $endPage = min($totalPages, $currentPage + 2);
                
                for ($i = $startPage; $i <= $endPage; $i++):
                ?>
                    <?php if ($i == $currentPage): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($currentPage < $totalPages): ?>
                    <a href="?page=<?php echo $currentPage + 1; ?>">Next ›</a>
                    <a href="?page=<?php echo $totalPages; ?>">Last »</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<div style="margin-top: 30px; text-align: center;">
    <a href="compose.php" class="btn">✏️ Compose New Email</a>
    <a href="javascript:location.reload()" class="btn btn-secondary">🔄 Refresh</a>
</div>

<?php include 'includes/footer.php'; ?>
