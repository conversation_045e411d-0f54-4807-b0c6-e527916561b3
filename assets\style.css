/* Email Client Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    text-align: center;
    font-size: 2.5em;
    font-weight: 300;
}

/* Navigation */
.nav {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

.nav a {
    text-decoration: none;
    color: #667eea;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.nav a:hover, .nav a.active {
    background: #667eea;
    color: white;
}

/* Email List */
.email-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.email-item:hover {
    background-color: #f8f9fa;
}

.email-item:last-child {
    border-bottom: none;
}

.email-item.unread {
    background-color: #f0f8ff;
    font-weight: 600;
}

.email-status {
    width: 20px;
    text-align: center;
}

.email-status .unread-dot {
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
    display: inline-block;
}

.email-content {
    flex: 1;
}

.email-subject {
    font-size: 1.1em;
    margin-bottom: 5px;
    color: #333;
}

.email-from {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 3px;
}

.email-date {
    color: #999;
    font-size: 0.85em;
}

.email-size {
    color: #999;
    font-size: 0.8em;
    min-width: 60px;
    text-align: right;
}

/* Email View */
.email-view {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-header {
    border-bottom: 2px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.email-subject-large {
    font-size: 1.8em;
    color: #333;
    margin-bottom: 15px;
}

.email-meta {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px 20px;
    font-size: 0.9em;
}

.email-meta-label {
    font-weight: 600;
    color: #666;
}

.email-meta-value {
    color: #333;
}

.email-body {
    font-size: 1em;
    line-height: 1.8;
    color: #444;
}

.email-body pre {
    white-space: pre-wrap;
    font-family: inherit;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 200px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: #667eea;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
}

.btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination a, .pagination span {
    padding: 10px 15px;
    background: white;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #667eea;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: #667eea;
    color: white;
}

.pagination .current {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Alerts */
.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .nav ul {
        flex-direction: column;
        gap: 10px;
    }
    
    .email-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .email-meta {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

/* Loading spinner */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
