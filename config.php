<?php
/**
 * Email Client Configuration
 * 
 * Configure your email settings here
 */

// Email Server Configuration
define('MAIL_SERVER', 'mail.yourdomain.com'); // Replace with your mail server
define('MAIL_PORT', '993'); // IMAP SSL port
define('MAIL_FLAGS', '/imap/ssl'); // IMAP flags

// Email Credentials (Replace with your actual credentials)
define('EMAIL_ADDRESS', '<EMAIL>');
define('EMAIL_PASSWORD', 'yourEmailPassword');

// SMTP Configuration for sending emails
define('SMTP_SERVER', 'mail.yourdomain.com');
define('SMTP_PORT', '587'); // or 465 for SSL
define('SMTP_USERNAME', EMAIL_ADDRESS);
define('SMTP_PASSWORD', EMAIL_PASSWORD);

// Application Settings
define('EMAILS_PER_PAGE', 20);
define('APP_NAME', 'PHP Email Client');

// Security Settings
session_start();

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if IMAP extension is loaded
if (!extension_loaded('imap')) {
    die('IMAP extension is not loaded. Please enable php_imap extension.');
}

/**
 * Get IMAP connection string
 */
function getImapConnection() {
    return '{' . MAIL_SERVER . ':' . MAIL_PORT . MAIL_FLAGS . '}INBOX';
}

/**
 * Sanitize output for display
 */
function sanitizeOutput($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

/**
 * Format file size
 */
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
