XAMPP PHP IMAP Extension Setup Instructions
===========================================

Method 1: Edit php.ini file (Recommended)
-----------------------------------------

1. Open XAMPP Control Panel
2. Click "Config" button next to Apache
3. Select "PHP (php.ini)"
4. Find the line: ;extension=imap
5. Remove the semicolon (;) to uncomment it: extension=imap
6. Save the file
7. Restart Apache in XAMPP Control Panel

Method 2: Manual php.ini edit
----------------------------

1. Navigate to: C:\xampp\php\php.ini
2. Open php.ini in a text editor (as Administrator)
3. Search for: ;extension=imap
4. Change it to: extension=imap (remove the semicolon)
5. Save the file
6. Restart Apache

Method 3: If IMAP extension is missing
-------------------------------------

If the imap line doesn't exist in php.ini:
1. Add this line in the extensions section: extension=imap
2. Make sure php_imap.dll exists in C:\xampp\php\ext\
3. If missing, you may need to download it or reinstall XAMPP

Verification
-----------

After restarting Apache:
1. Create a test file with: <?php phpinfo(); ?>
2. Look for "imap" in the output
3. Or run: php -m | findstr imap

Troubleshooting
--------------

If IMAP still doesn't work:
- Check if php_imap.dll exists in C:\xampp\php\ext\
- Ensure you restarted Apache after making changes
- Check Apache error logs for any issues
- Try restarting the entire XAMPP stack
