<?php
/**
 * Email Client - Compose Email
 */

require_once 'config.php';
require_once 'email_functions.php';

$pageTitle = 'Compose Email';

// Handle reply
$replyTo = '';
$replySubject = '';
$replyBody = '';

if (isset($_GET['reply']) && $_GET['reply']) {
    try {
        $replyEmailId = (int)$_GET['reply'];
        $replyEmail = getEmail($replyEmailId);
        
        $from = $replyEmail['from'];
        $replyTo = $from->mailbox . '@' . $from->host;
        
        $subject = $replyEmail['subject'];
        if (!preg_match('/^Re:/i', $subject)) {
            $replySubject = 'Re: ' . $subject;
        } else {
            $replySubject = $subject;
        }
        
        // Quote original message
        $originalBody = strip_tags($replyEmail['body']);
        $replyBody = "\n\n--- Original Message ---\n";
        $replyBody .= "From: " . $replyTo . "\n";
        $replyBody .= "Date: " . date('F j, Y \a\t g:i A', strtotime($replyEmail['date'])) . "\n";
        $replyBody .= "Subject: " . $replyEmail['subject'] . "\n\n";
        $replyBody .= $originalBody;
        
    } catch (Exception $e) {
        $_SESSION['message'] = 'Error loading email for reply: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $to = trim($_POST['to']);
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);
    
    $errors = [];
    
    // Validation
    if (empty($to)) {
        $errors[] = 'Recipient email is required';
    } elseif (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid recipient email address';
    }
    
    if (empty($subject)) {
        $errors[] = 'Subject is required';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required';
    }
    
    if (empty($errors)) {
        try {
            // Convert line breaks to HTML for better formatting
            $htmlMessage = nl2br(sanitizeOutput($message));
            
            if (sendEmail($to, $subject, $htmlMessage)) {
                $_SESSION['message'] = 'Email sent successfully to ' . $to;
                $_SESSION['message_type'] = 'success';
                header('Location: index.php');
                exit;
            } else {
                $errors[] = 'Failed to send email. Please check your mail server configuration.';
            }
        } catch (Exception $e) {
            $errors[] = 'Error sending email: ' . $e->getMessage();
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['message'] = implode('<br>', $errors);
        $_SESSION['message_type'] = 'error';
    }
}

include 'includes/header.php';
?>

<div class="email-view">
    <div style="margin-bottom: 20px;">
        <a href="index.php" class="btn btn-secondary">← Back to Inbox</a>
    </div>
    
    <h2>✏️ Compose New Email</h2>
    
    <form method="post" style="margin-top: 30px;">
        <div class="form-group">
            <label for="to">To:</label>
            <input type="email" 
                   id="to" 
                   name="to" 
                   class="form-control" 
                   value="<?php echo sanitizeOutput($replyTo); ?>"
                   placeholder="<EMAIL>"
                   required>
        </div>
        
        <div class="form-group">
            <label for="subject">Subject:</label>
            <input type="text" 
                   id="subject" 
                   name="subject" 
                   class="form-control" 
                   value="<?php echo sanitizeOutput($replySubject); ?>"
                   placeholder="Enter email subject"
                   required>
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" 
                      name="message" 
                      class="form-control" 
                      rows="15"
                      placeholder="Type your message here..."
                      required><?php echo sanitizeOutput($replyBody); ?></textarea>
        </div>
        
        <div style="margin-top: 30px;">
            <button type="submit" class="btn">📤 Send Email</button>
            <a href="index.php" class="btn btn-secondary" style="margin-left: 10px;">Cancel</a>
        </div>
    </form>
</div>

<script>
// Auto-save draft functionality (basic)
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea');
    
    // Load saved draft
    inputs.forEach(function(input) {
        const saved = localStorage.getItem('draft_' + input.name);
        if (saved && !input.value) {
            input.value = saved;
        }
    });
    
    // Save draft on input
    inputs.forEach(function(input) {
        input.addEventListener('input', function() {
            localStorage.setItem('draft_' + this.name, this.value);
        });
    });
    
    // Clear draft on successful send
    form.addEventListener('submit', function() {
        inputs.forEach(function(input) {
            localStorage.removeItem('draft_' + input.name);
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
