<?php
/**
 * Email Client - Settings & Configuration
 */

require_once 'config.php';

$pageTitle = 'Settings';

// Test connection
$connectionStatus = '';
$connectionClass = '';

if (isset($_POST['test_connection'])) {
    try {
        require_once 'email_functions.php';
        $inbox = connectToImap();
        $status = imap_status($inbox, getImapConnection(), SA_ALL);
        imap_close($inbox);
        
        $connectionStatus = 'Connection successful! Found ' . $status->messages . ' messages in inbox.';
        $connectionClass = 'alert-success';
    } catch (Exception $e) {
        $connectionStatus = 'Connection failed: ' . $e->getMessage();
        $connectionClass = 'alert-error';
    }
}

include 'includes/header.php';
?>

<div class="email-view">
    <h2>⚙️ Email Client Settings</h2>
    
    <?php if ($connectionStatus): ?>
        <div class="alert <?php echo $connectionClass; ?>">
            <?php echo $connectionStatus; ?>
        </div>
    <?php endif; ?>
    
    <div style="margin-top: 30px;">
        <h3>📧 Current Email Configuration</h3>
        
        <div class="email-meta" style="margin-top: 20px;">
            <span class="email-meta-label">Email Address:</span>
            <span class="email-meta-value"><?php echo sanitizeOutput(EMAIL_ADDRESS); ?></span>
            
            <span class="email-meta-label">Mail Server:</span>
            <span class="email-meta-value"><?php echo sanitizeOutput(MAIL_SERVER); ?></span>
            
            <span class="email-meta-label">IMAP Port:</span>
            <span class="email-meta-value"><?php echo MAIL_PORT; ?></span>
            
            <span class="email-meta-label">Connection:</span>
            <span class="email-meta-value"><?php echo MAIL_FLAGS; ?></span>
            
            <span class="email-meta-label">SMTP Server:</span>
            <span class="email-meta-value"><?php echo sanitizeOutput(SMTP_SERVER); ?></span>
            
            <span class="email-meta-label">SMTP Port:</span>
            <span class="email-meta-value"><?php echo SMTP_PORT; ?></span>
            
            <span class="email-meta-label">Emails per page:</span>
            <span class="email-meta-value"><?php echo EMAILS_PER_PAGE; ?></span>
        </div>
        
        <div style="margin-top: 30px;">
            <form method="post" style="display: inline-block;">
                <button type="submit" name="test_connection" class="btn">🔍 Test Connection</button>
            </form>
            <a href="index.php" class="btn btn-secondary" style="margin-left: 10px;">← Back to Inbox</a>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee;">
        <h3>🔧 How to Configure</h3>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <p><strong>To configure this email client for your cPanel email:</strong></p>
            
            <ol style="margin-top: 15px; padding-left: 20px;">
                <li>Open <code>config.php</code> in a text editor</li>
                <li>Replace the following values:
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><code>MAIL_SERVER</code> - Your mail server (e.g., mail.yourdomain.com)</li>
                        <li><code>EMAIL_ADDRESS</code> - Your full email address</li>
                        <li><code>EMAIL_PASSWORD</code> - Your email password</li>
                        <li><code>SMTP_SERVER</code> - Your SMTP server (usually same as mail server)</li>
                    </ul>
                </li>
                <li>Save the file and test the connection</li>
            </ol>
            
            <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px;">
                <strong>⚠️ Security Note:</strong> Never commit your actual email credentials to version control. 
                Consider using environment variables or a separate config file for production.
            </div>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee;">
        <h3>📋 System Information</h3>
        
        <div class="email-meta" style="margin-top: 20px;">
            <span class="email-meta-label">PHP Version:</span>
            <span class="email-meta-value"><?php echo PHP_VERSION; ?></span>
            
            <span class="email-meta-label">IMAP Extension:</span>
            <span class="email-meta-value">
                <?php echo extension_loaded('imap') ? '✅ Loaded' : '❌ Not loaded'; ?>
            </span>
            
            <span class="email-meta-label">OpenSSL Extension:</span>
            <span class="email-meta-value">
                <?php echo extension_loaded('openssl') ? '✅ Loaded' : '❌ Not loaded'; ?>
            </span>
            
            <span class="email-meta-label">Mail Function:</span>
            <span class="email-meta-value">
                <?php echo function_exists('mail') ? '✅ Available' : '❌ Not available'; ?>
            </span>
            
            <span class="email-meta-label">Server Time:</span>
            <span class="email-meta-value"><?php echo date('Y-m-d H:i:s T'); ?></span>
        </div>
    </div>
    
    <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee;">
        <h3>🆘 Troubleshooting</h3>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;">
            <h4>Common Issues:</h4>
            
            <ul style="margin-top: 15px; padding-left: 20px;">
                <li><strong>IMAP extension not loaded:</strong> Enable php_imap in your PHP configuration</li>
                <li><strong>SSL connection failed:</strong> Try using port 143 without SSL or check your server's SSL certificate</li>
                <li><strong>Authentication failed:</strong> Verify your email address and password</li>
                <li><strong>Can't send emails:</strong> Check your SMTP settings and make sure mail() function is working</li>
                <li><strong>Timeout errors:</strong> Your server might be blocking the connection or the mail server is slow</li>
            </ul>
            
            <div style="margin-top: 20px; padding: 15px; background: #d1ecf1; border-radius: 5px;">
                <strong>💡 Tip:</strong> Contact your hosting provider if you're having trouble with email server settings. 
                They can provide the correct IMAP/SMTP configuration for your account.
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
