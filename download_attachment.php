<?php
/**
 * Email Client - Download Attachment
 */

require_once 'config.php';
require_once 'email_functions.php';

// Get parameters
$emailId = isset($_GET['email']) ? (int)$_GET['email'] : 0;
$part = isset($_GET['part']) ? (int)$_GET['part'] : 0;
$filename = isset($_GET['filename']) ? $_GET['filename'] : 'attachment';

if (!$emailId || !$part) {
    die('Invalid parameters');
}

try {
    $inbox = connectToImap();
    
    // Get attachment data
    $data = imap_fetchbody($inbox, $emailId, $part);
    $structure = imap_fetchstructure($inbox, $emailId);
    
    // Find the specific part
    $attachmentPart = null;
    if (isset($structure->parts) && count($structure->parts)) {
        $partIndex = $part - 1;
        if (isset($structure->parts[$partIndex])) {
            $attachmentPart = $structure->parts[$partIndex];
        }
    }
    
    if (!$attachmentPart) {
        die('Attachment not found');
    }
    
    // Decode based on encoding
    if ($attachmentPart->encoding == 3) { // Base64
        $data = base64_decode($data);
    } elseif ($attachmentPart->encoding == 4) { // Quoted-printable
        $data = quoted_printable_decode($data);
    }
    
    // Get MIME type
    $mimeType = 'application/octet-stream';
    if (isset($attachmentPart->subtype)) {
        $primaryType = $attachmentPart->type == 0 ? 'text' : 
                      ($attachmentPart->type == 1 ? 'multipart' :
                      ($attachmentPart->type == 2 ? 'message' :
                      ($attachmentPart->type == 3 ? 'application' :
                      ($attachmentPart->type == 4 ? 'audio' :
                      ($attachmentPart->type == 5 ? 'image' :
                      ($attachmentPart->type == 6 ? 'video' : 'other'))))));
        
        $mimeType = $primaryType . '/' . strtolower($attachmentPart->subtype);
    }
    
    imap_close($inbox);
    
    // Set headers for download
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($data));
    header('Cache-Control: private');
    header('Pragma: private');
    header('Expires: 0');
    
    // Output the file
    echo $data;
    
} catch (Exception $e) {
    die('Error downloading attachment: ' . $e->getMessage());
}
?>
